"use client"

import { useState } from "react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { PhonebookKey } from "@/lib/phonebook-keys"

interface RegenerateSecretDialogProps {
  keyData: PhonebookKey | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm: (keyId: string) => Promise<void>
}

export function RegenerateSecretDialog({ 
  keyData, 
  open, 
  onOpenChange, 
  onConfirm 
}: RegenerateSecretDialogProps) {
  const [isRegenerating, setIsRegenerating] = useState(false)

  const handleConfirm = async () => {
    if (!keyData) return
    
    setIsRegenerating(true)
    try {
      await onConfirm(keyData.id)
      onOpenChange(false)
    } catch (error) {
      console.error('Error regenerating secret:', error)
    } finally {
      setIsRegenerating(false)
    }
  }

  const formatKey = (key: string) => {
    if (key.length <= 12) return key
    return `${key.slice(0, 8)}...${key.slice(-4)}`
  }

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Regenerate Secret</AlertDialogTitle>
          <AlertDialogDescription className="space-y-2">
            <p>
              Are you sure you want to regenerate the secret for this API key? This action cannot be undone.
            </p>
            {keyData && (
              <div className="bg-muted p-3 rounded-md">
                <p className="text-sm font-medium">API Key:</p>
                <code className="text-sm font-mono">
                  {formatKey(keyData.key)}
                </code>
              </div>
            )}
            <p className="text-sm text-destructive font-medium">
              The old secret will be immediately invalidated. Any applications using the old secret will lose access until updated with the new secret.
            </p>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isRegenerating}>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={isRegenerating}
          >
            {isRegenerating ? "Regenerating..." : "Regenerate Secret"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
